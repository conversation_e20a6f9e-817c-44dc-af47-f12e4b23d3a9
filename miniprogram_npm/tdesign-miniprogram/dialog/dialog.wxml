<import src="../common/template/button.wxml" />
<import src="../common/template/icon.wxml" />
<wxs src="../common/utils.wxs" module="_" />
<wxs src="./dialog.wxs" module="_this" />

<t-popup
  name="dialog"
  style="{{_._style([style, customStyle])}}"
  class="class"
  t-class="{{classPrefix}}__wrapper"
  visible="{{visible}}"
  showOverlay="{{showOverlay}}"
  closeOnOverlayClick="{{closeOnOverlayClick}}"
  preventScrollThrough="{{preventScrollThrough}}"
  overlayProps="{{overlayProps}}"
  zIndex="{{zIndex}}"
  placement="center"
  usingCustomNavbar="{{usingCustomNavbar}}"
  bind:visible-change="overlayClick"
>
  <view slot="content" class="{{classPrefix}} {{prefix}}-class">
    <slot name="top" />
    <view wx:if="{{closeBtn}}" class="{{classPrefix}}__close-btn" bind:tap="onClose">
      <template wx:if="{{_.isObject(closeBtn)}}" is="icon" data="{{ name: 'close', size: 22, ...closeBtn }}" />
      <t-icon wx:else name="close" size="44rpx" />
    </view>
    <view class="{{classPrefix}}__content {{prefix}}-class-content">
      <view wx:if="{{title}}" class="{{classPrefix}}__header">{{title}}</view>
      <slot name="title" />
      <view wx:if="{{content}}" class="{{classPrefix}}__body">
        <text class="{{classPrefix}}__body-text">{{content}}</text>
      </view>
      <slot name="content" />
    </view>
    <slot name="middle" />
    <view
      class="{{_.cls(classPrefix + '__footer', [['column', buttonLayout === 'vertical'], ['full', buttonVariant == 'text' && actions.length == 0]])}}"
    >
      <block wx:if="{{actions}}">
        <block wx:for="{{actions}}" wx:key="index">
          <template
            is="button"
            data="{{block: true, type: 'action', extra: index, tClass: prefix + '-class-action', rootClass: _this.getActionClass(classPrefix, buttonLayout), ...item }}"
          />
        </block>
      </block>
      <slot name="actions" />
      <block wx:if="{{_cancel}}">
        <template is="button" data="{{type: 'cancel', ..._cancel }}" />
      </block>
      <slot name="cancel-btn" />
      <block wx:if="{{_confirm}}">
        <template is="button" data="{{type: 'confirm', theme: 'primary', ..._confirm}}" />
      </block>
      <slot name="confirm-btn" />
    </view>
  </view>
</t-popup>
