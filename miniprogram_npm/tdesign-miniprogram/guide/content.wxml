<import src="../common/template/button.wxml" />
<template name="content">
  <slot wx:if="{{!title && !body}}" name="content-{{current}}" />
  <block wx:else>
    <view class="{{prefix}}-class-tooltip">
      <view wx:if="{{title}}" class="{{prefix}}-class-title {{classPrefix}}__title--{{modeType}}">{{title}}</view>
      <slot wx:else name="title-{{current}}" />
      <view wx:if="{{body}}" class="{{prefix}}-class-body {{classPrefix}}__body--{{modeType}}">{{body}}</view>
      <slot wx:else name="body-{{current}}" />
    </view>
    <view
      wx:if="{{current !== -1}}"
      class="{{prefix}}-class-footer {{classPrefix}}__footer {{classPrefix}}__footer--{{modeType}}"
    >
      <template is="button" wx:if="{{current < steps.length - 1 && !hideSkip}}" data="{{...skipButton}}" />
      <template is="button" wx:elif="{{current === steps.length - 1 && !hideBack}}" data="{{...backButton}}" />
      <template is="button" wx:if="{{current < steps.length - 1}}" data="{{...nextButton}}" />
      <template is="button" wx:else data="{{...finishButton}}" />
    </view>
  </block>
</template>
