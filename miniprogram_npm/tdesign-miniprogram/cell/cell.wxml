<import src="../common/template/icon.wxml" />
<wxs src="../common/utils.wxs" module="_" />

<view
  style="{{_._style([style, customStyle])}}"
  class="class {{prefix}}-class {{_.cls(classPrefix, [['borderless', !bordered || isLastChild]])}}"
  hover-class="{{ hover ? classPrefix + '--hover' : ''}}"
  hover-stay-time="70"
  bind:tap="onClick"
  aria-role="{{ariaRole || (arrow ? 'button' : '')}}"
  aria-label="{{ariaLabel}}"
>
  <view class="{{classPrefix}}__left {{prefix}}-class-left">
    <template
      wx:if="{{_leftIcon}}"
      is="icon"
      data="{{tClass: classPrefix + '__left-icon ' + prefix + '-class-left-icon', ..._leftIcon }}"
    />
    <slot name="left-icon" />
    <t-image
      wx:if="{{ image }}"
      shape="round"
      t-class="{{classPrefix}}__left-image {{prefix}}-class-image"
      src="{{ image }}"
    />
    <slot name="image" />
  </view>
  <view class="{{classPrefix}}__title {{prefix}}-class-center">
    <view class="{{classPrefix}}__title-text {{prefix}}-class-title">
      <block wx:if="{{ title }}"> {{ title}} </block>
      <slot name="title" />
      <block wx:if="{{required}}">
        <text decode class="{{classPrefix}}--required">&nbsp;*</text>
      </block>
    </view>

    <view class="{{classPrefix}}__description {{prefix}}-class-description">
      <view wx:if="{{ description }}" class="{{classPrefix}}__description-text">{{description}}</view>
      <slot name="description" />
    </view>
  </view>

  <view class="{{classPrefix}}__note {{prefix}}-class-note">
    <text wx:if="{{ note }}">{{note}}</text>
    <slot name="note" />
  </view>

  <view class="{{_.cls(classPrefix + '__right', [align])}} {{prefix}}-class-right">
    <template
      wx:if="{{_arrow}}"
      is="icon"
      data="{{tClass: classPrefix + '__right-icon ' + prefix + '-class-right-icon', ..._arrow }}"
    />
    <block wx:else>
      <template
        wx:if="{{_rightIcon}}"
        is="icon"
        data="{{tClass: classPrefix + '__right-icon ' + prefix + '-class-right-icon', ..._rightIcon }}"
      />
      <slot name="right-icon" />
    </block>
  </view>
</view>
